<Window x:Class="InventoryBillingSystem.WPF.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Inventory Billing System - Login"
        Height="400"
        Width="350"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">

    <Border Padding="30">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Title -->
            <TextBlock Grid.Row="0"
                      Text="Inventory Billing System"
                      FontSize="24"
                      FontWeight="Bold"
                      HorizontalAlignment="Center"
                      Margin="0,0,0,30"/>

            <!-- Username -->
            <TextBlock Grid.Row="1"
                      Text="Username:"
                      Margin="0,5"/>
            <TextBox Grid.Row="2"
                    Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                    Margin="0,5"
                    Padding="8"
                    />

            <!-- Password -->
            <TextBlock Grid.Row="3"
                      Text="Password:"
                      Margin="0,5"/>
            <PasswordBox Grid.Row="4"
                        x:Name="PasswordBox"
                        Margin="0,5"
                        Padding="8"/>

            <!-- Error Message -->
            <TextBlock Grid.Row="5"
                      Text="{Binding ErrorMessage}"
                      Foreground="Red"
                      FontSize="12"
                      TextWrapping="Wrap"
                      Margin="0,10"/>

            <!-- Login Button -->
            <Button Grid.Row="6"
                   Content="Login"
                   Command="{Binding LoginCommand}"
                   Margin="0,20"
                   Padding="20,10"
                   HorizontalAlignment="Center">
                <Button.CommandParameter>
                    <Binding ElementName="PasswordBox" Path="Password"/>
                </Button.CommandParameter>
            </Button>
        </Grid>
    </Border>
</Window>
