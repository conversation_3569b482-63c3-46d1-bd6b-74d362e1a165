<Window x:Class="InventoryBillingSystem.WPF.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Inventory Billing System - Login"
        Height="650"
        Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        AllowsTransparency="True"
        WindowStyle="None"
        Background="Transparent"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.5"/>
        </Storyboard>
    </Window.Resources>

    <Window.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource FadeInAnimation}"/>
        </EventTrigger>
    </Window.Triggers>

    <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp8"
                         Margin="20">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="200"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header with Gradient Background -->
            <Border Grid.Row="0"
                    Background="{StaticResource PrimaryGradientBrush}"
                    CornerRadius="8,8,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Background Pattern -->
                    <Canvas Grid.RowSpan="2" Opacity="0.1">
                        <Ellipse Width="120" Height="120" Fill="White" Canvas.Left="-30" Canvas.Top="-30"/>
                        <Ellipse Width="80" Height="80" Fill="White" Canvas.Right="-20" Canvas.Bottom="-20"/>
                        <Ellipse Width="60" Height="60" Fill="White" Canvas.Right="50" Canvas.Top="20"/>
                    </Canvas>

                    <!-- Logo and Title -->
                    <StackPanel Grid.Row="0"
                                Orientation="Vertical"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center">
                        <Border Background="White"
                                CornerRadius="30"
                                Width="60"
                                Height="60"
                                materialDesign:ElevationAssist.Elevation="Dp4">
                            <materialDesign:PackIcon Kind="Store"
                                                   Width="32"
                                                   Height="32"
                                                   Foreground="{StaticResource PrimaryGradientBrush}"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="Inventory Billing System"
                                  Style="{StaticResource HeaderTextStyle}"
                                  FontSize="18"
                                  HorizontalAlignment="Center"
                                  Margin="0,16,0,0"/>
                        <TextBlock Text="Professional Business Management"
                                  Foreground="White"
                                  FontSize="12"
                                  Opacity="0.8"
                                  HorizontalAlignment="Center"
                                  Margin="0,4,0,0"/>
                    </StackPanel>

                    <!-- Welcome Message -->
                    <TextBlock Grid.Row="1"
                              Text="Welcome Back!"
                              Foreground="White"
                              FontSize="24"
                              FontWeight="Light"
                              HorizontalAlignment="Center"
                              Margin="0,0,0,20"/>
                </Grid>
            </Border>

            <!-- Login Form -->
            <StackPanel Grid.Row="1"
                        Margin="40,30,40,20"
                        VerticalAlignment="Center">

                <!-- Username -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp1"
                                     Margin="0,0,0,20"
                                     Padding="0">
                    <TextBox materialDesign:HintAssist.Hint="Username or Email"
                             materialDesign:HintAssist.IsFloating="True"
                             materialDesign:TextFieldAssist.HasLeadingIcon="True"
                             materialDesign:TextFieldAssist.LeadingIcon="Account"
                             Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             FontSize="14"
                             Padding="16,12"
                             IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"/>
                </materialDesign:Card>

                <!-- Password -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp1"
                                     Margin="0,0,0,20"
                                     Padding="0">
                    <PasswordBox x:Name="PasswordBox"
                                materialDesign:HintAssist.Hint="Password"
                                materialDesign:HintAssist.IsFloating="True"
                                materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                materialDesign:TextFieldAssist.LeadingIcon="Lock"
                                Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                                FontSize="14"
                                Padding="16,12"
                                IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"/>
                </materialDesign:Card>

                <!-- Remember Me and Forgot Password -->
                <Grid Margin="0,0,0,24">
                    <CheckBox Content="Remember Me"
                             IsChecked="{Binding RememberMe}"
                             HorizontalAlignment="Left"
                             FontSize="12"
                             IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"/>
                    <Button Content="Forgot Password?"
                           HorizontalAlignment="Right"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           FontSize="12"
                           Padding="8,4"
                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                </Grid>

                <!-- Error Message -->
                <materialDesign:Card Background="{DynamicResource MaterialDesignValidationErrorBrush}"
                                     Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                     Margin="0,0,0,16"
                                     Padding="16,12">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AlertCircle"
                                               Width="16"
                                               Height="16"
                                               Foreground="White"
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding ErrorMessage}"
                                  Foreground="White"
                                  FontSize="12"
                                  TextWrapping="Wrap"
                                  Margin="8,0,0,0"
                                  VerticalAlignment="Center"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Login Button -->
                <Button Command="{Binding LoginCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Background="{StaticResource PrimaryGradientBrush}"
                       BorderBrush="{StaticResource PrimaryGradientBrush}"
                       Height="48"
                       FontSize="14"
                       FontWeight="Medium"
                       materialDesign:ElevationAssist.Elevation="Dp4"
                       materialDesign:ButtonAssist.CornerRadius="8">
                    <Button.CommandParameter>
                        <Binding ElementName="PasswordBox" Path="Password"/>
                    </Button.CommandParameter>
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Login"
                                                   Width="18"
                                                   Height="18"
                                                   VerticalAlignment="Center"
                                                   Visibility="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"/>
                            <ProgressBar Width="18"
                                        Height="18"
                                        Style="{StaticResource MaterialDesignCircularProgressBar}"
                                        IsIndeterminate="True"
                                        Foreground="White"
                                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            <TextBlock Text="SIGN IN"
                                      Margin="8,0,0,0"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

            </StackPanel>

            <!-- Footer -->
            <Border Grid.Row="2"
                    Background="{DynamicResource MaterialDesignCardBackground}"
                    CornerRadius="0,0,8,8"
                    Padding="20,16">
                <Grid>
                    <TextBlock Text="© 2024 Inventory Billing System. All rights reserved."
                              FontSize="10"
                              Opacity="0.6"
                              HorizontalAlignment="Left"
                              VerticalAlignment="Center"/>
                    <Button Content="EXIT"
                           Command="{Binding ExitCommand}"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           HorizontalAlignment="Right"
                           FontSize="12"
                           Padding="16,8"/>
                </Grid>
            </Border>
        </Grid>
    </materialDesign:Card>
</Window>
