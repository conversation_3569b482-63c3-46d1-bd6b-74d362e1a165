using InventoryBillingSystem.Core.DTOs;
using InventoryBillingSystem.Core.Entities;
using InventoryBillingSystem.Core.Enums;
using InventoryBillingSystem.Core.Interfaces;
using InventoryBillingSystem.Core.Utilities;

namespace InventoryBillingSystem.Services
{
    public class UserService : IUserService
    {
        private readonly IUnitOfWork _unitOfWork;

        public UserService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<UserDto>> GetAllUsersAsync()
        {
            var users = await _unitOfWork.Users.GetAllAsync();
            return users.Select(MapToDto);
        }

        public async Task<UserDto?> GetUserByIdAsync(int id)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(id);
            return user != null ? MapToDto(user) : null;
        }

        public async Task<UserDto?> GetUserByUsernameAsync(string username)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == username);
            return user != null ? MapToDto(user) : null;
        }

        public async Task<UserDto?> GetUserByEmailAsync(string email)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Email == email);
            return user != null ? MapToDto(user) : null;
        }

        public async Task<UserDto> CreateUserAsync(CreateUserDto createUserDto)
        {
            var user = new User
            {
                Username = createUserDto.Username,
                Email = createUserDto.Email,
                FirstName = createUserDto.FirstName,
                LastName = createUserDto.LastName,
                PasswordHash = PasswordHasher.HashPassword(createUserDto.Password),
                PhoneNumber = createUserDto.PhoneNumber,
                Role = createUserDto.Role,
                IsActive = createUserDto.IsActive
            };

            await _unitOfWork.Users.AddAsync(user);
            await _unitOfWork.SaveChangesAsync();

            return MapToDto(user);
        }

        public async Task<UserDto> UpdateUserAsync(UpdateUserDto updateUserDto)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(updateUserDto.Id);
            if (user == null)
            {
                throw new ArgumentException("User not found");
            }

            user.Username = updateUserDto.Username;
            user.Email = updateUserDto.Email;
            user.FirstName = updateUserDto.FirstName;
            user.LastName = updateUserDto.LastName;
            user.PhoneNumber = updateUserDto.PhoneNumber;
            user.Role = updateUserDto.Role;
            user.IsActive = updateUserDto.IsActive;
            user.ProfileImagePath = updateUserDto.ProfileImagePath;

            _unitOfWork.Users.Update(user);
            await _unitOfWork.SaveChangesAsync();

            return MapToDto(user);
        }

        public async Task<bool> DeleteUserAsync(int id)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(id);
            if (user == null)
            {
                return false;
            }

            _unitOfWork.Users.SoftDelete(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ActivateUserAsync(int id)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(id);
            if (user == null)
            {
                return false;
            }

            user.IsActive = true;
            _unitOfWork.Users.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeactivateUserAsync(int id)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(id);
            if (user == null)
            {
                return false;
            }

            user.IsActive = false;
            _unitOfWork.Users.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<UserDto>> GetUsersByRoleAsync(UserRole role)
        {
            var users = await _unitOfWork.Users.FindAsync(u => u.Role == role);
            return users.Select(MapToDto);
        }

        public async Task<bool> IsUsernameAvailableAsync(string username, int? excludeUserId = null)
        {
            if (excludeUserId.HasValue)
            {
                return !await _unitOfWork.Users.AnyAsync(u => u.Username == username && u.Id != excludeUserId.Value);
            }
            return !await _unitOfWork.Users.AnyAsync(u => u.Username == username);
        }

        public async Task<bool> IsEmailAvailableAsync(string email, int? excludeUserId = null)
        {
            if (excludeUserId.HasValue)
            {
                return !await _unitOfWork.Users.AnyAsync(u => u.Email == email && u.Id != excludeUserId.Value);
            }
            return !await _unitOfWork.Users.AnyAsync(u => u.Email == email);
        }

        public async Task<IEnumerable<UserDto>> SearchUsersAsync(string searchTerm)
        {
            var users = await _unitOfWork.Users.FindAsync(u => 
                u.Username.Contains(searchTerm) ||
                u.Email.Contains(searchTerm) ||
                u.FirstName.Contains(searchTerm) ||
                u.LastName.Contains(searchTerm));
            
            return users.Select(MapToDto);
        }

        public async Task<(IEnumerable<UserDto> Users, int TotalCount)> GetPagedUsersAsync(int pageNumber, int pageSize, string? searchTerm = null)
        {
            IEnumerable<User> users;
            int totalCount;

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                users = await _unitOfWork.Users.GetPagedAsync(pageNumber, pageSize);
                totalCount = await _unitOfWork.Users.CountAsync();
            }
            else
            {
                users = await _unitOfWork.Users.GetPagedAsync(pageNumber, pageSize, u => 
                    u.Username.Contains(searchTerm) ||
                    u.Email.Contains(searchTerm) ||
                    u.FirstName.Contains(searchTerm) ||
                    u.LastName.Contains(searchTerm));
                
                totalCount = await _unitOfWork.Users.CountAsync(u => 
                    u.Username.Contains(searchTerm) ||
                    u.Email.Contains(searchTerm) ||
                    u.FirstName.Contains(searchTerm) ||
                    u.LastName.Contains(searchTerm));
            }

            return (users.Select(MapToDto), totalCount);
        }

        private static UserDto MapToDto(User user)
        {
            return new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                PhoneNumber = user.PhoneNumber,
                Role = user.Role,
                IsActive = user.IsActive,
                LastLoginAt = user.LastLoginAt,
                ProfileImagePath = user.ProfileImagePath,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            };
        }
    }
}
