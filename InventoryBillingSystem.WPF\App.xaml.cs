using System.Windows;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using InventoryBillingSystem.Core.Interfaces;
using InventoryBillingSystem.Data.Context;
using InventoryBillingSystem.Data.Repositories;
using InventoryBillingSystem.Services;
using InventoryBillingSystem.WPF.ViewModels;
using InventoryBillingSystem.WPF.Views;
using System.Runtime.InteropServices;

namespace InventoryBillingSystem.WPF;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    [DllImport("kernel32.dll", SetLastError = true)]
    [return: MarshalAs(UnmanagedType.Bool)]
    static extern bool AllocConsole();

    protected override void OnStartup(StartupEventArgs e)
    {
        AllocConsole();

        try
        {
            Console.WriteLine("Starting application...");

            // Create a simple test window instead of complex DI
            var testWindow = new Window
            {
                Title = "Inventory Billing System - Test",
                Width = 400,
                Height = 300,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                Content = new System.Windows.Controls.TextBlock
                {
                    Text = "Application is running successfully!\n\nThis is a test window to verify the app starts correctly.",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FontSize = 16,
                    Margin = new Thickness(20)
                }
            };

            Console.WriteLine("Test window created...");
            testWindow.Show();
            Console.WriteLine("Test window shown...");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Inner Exception: {ex.InnerException?.Message}");
            Console.WriteLine($"Stack Trace: {ex.StackTrace}");

            MessageBox.Show($"Application failed to start: {ex.Message}\n\nInner Exception: {ex.InnerException?.Message}",
                "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
            return;
        }

        base.OnStartup(e);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }
        base.OnExit(e);
    }

    private async Task InitializeDatabaseAsync()
    {
        try
        {
            using var scope = _host!.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<InventoryBillingDbContext>();

            // Ensure database is created
            await context.Database.EnsureCreatedAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Database initialization failed: {ex.Message}",
                "Database Error", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Database - Using SQLite for development (change to MySQL in production)
                services.AddDbContext<InventoryBillingDbContext>(options =>
                    options.UseSqlite("Data Source=InventoryBilling.db"));

                // Repository Pattern
                services.AddScoped<IUnitOfWork, UnitOfWork>();

                // Services
                services.AddScoped<IUserService, UserService>();
                services.AddScoped<IAuthenticationService, AuthenticationService>();

                // ViewModels
                services.AddTransient<LoginViewModel>();
                services.AddTransient<MainViewModel>();

                // Views
                services.AddTransient<LoginWindow>();
                services.AddTransient<MainWindow>();
            });
    }
}

