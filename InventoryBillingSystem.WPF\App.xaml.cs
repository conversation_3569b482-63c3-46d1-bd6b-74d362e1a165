using System.Windows;
using System.Windows.Controls;

namespace InventoryBillingSystem.WPF;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        try
        {
            // Create a simple login window
            var loginWindow = CreateLoginWindow();
            loginWindow.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Application failed to start: {ex.Message}",
                "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
            return;
        }

        base.OnStartup(e);
    }

    private Window CreateLoginWindow()
    {
        var grid = new Grid();
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        // Title
        var title = new TextBlock
        {
            Text = "Inventory Billing System",
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 20, 0, 30)
        };
        Grid.SetRow(title, 0);
        grid.Children.Add(title);

        // Username
        var usernameLabel = new TextBlock { Text = "Username:", Margin = new Thickness(0, 5) };
        Grid.SetRow(usernameLabel, 1);
        grid.Children.Add(usernameLabel);

        var usernameBox = new TextBox { Margin = new Thickness(0, 5), Padding = new Thickness(5) };
        Grid.SetRow(usernameBox, 2);
        grid.Children.Add(usernameBox);

        // Password
        var passwordLabel = new TextBlock { Text = "Password:", Margin = new Thickness(0, 5) };
        Grid.SetRow(passwordLabel, 3);
        grid.Children.Add(passwordLabel);

        var passwordBox = new PasswordBox { Margin = new Thickness(0, 5), Padding = new Thickness(5) };
        Grid.SetRow(passwordBox, 4);
        grid.Children.Add(passwordBox);

        // Login button
        var loginButton = new Button
        {
            Content = "Login",
            Margin = new Thickness(0, 20),
            Padding = new Thickness(20, 10),
            HorizontalAlignment = HorizontalAlignment.Center
        };
        Grid.SetRow(loginButton, 5);
        grid.Children.Add(loginButton);

        loginButton.Click += (s, e) =>
        {
            MessageBox.Show("Login functionality will be implemented here!", "Info");
        };

        var window = new Window
        {
            Title = "Login - Inventory Billing System",
            Width = 350,
            Height = 300,
            WindowStartupLocation = WindowStartupLocation.CenterScreen,
            Content = new Border
            {
                Padding = new Thickness(30),
                Child = grid
            }
        };

        return window;
    }
}

