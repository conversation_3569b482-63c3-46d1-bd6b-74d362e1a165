using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using InventoryBillingSystem.Core.Interfaces;
using InventoryBillingSystem.Data.Context;
using InventoryBillingSystem.Data.Repositories;
using InventoryBillingSystem.Services;
using InventoryBillingSystem.WPF.ViewModels;
using InventoryBillingSystem.WPF.Views;

namespace InventoryBillingSystem.WPF;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        try
        {
            _host = CreateHostBuilder().Build();
            await _host.StartAsync();

            // Initialize database
            await InitializeDatabaseAsync();

            var loginWindow = _host.Services.GetRequiredService<LoginWindow>();
            loginWindow.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Application failed to start: {ex.Message}\n\nInner Exception: {ex.InnerException?.Message}",
                "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
            return;
        }

        base.OnStartup(e);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }
        base.OnExit(e);
    }

    private async Task InitializeDatabaseAsync()
    {
        try
        {
            using var scope = _host!.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<InventoryBillingDbContext>();
            var userService = scope.ServiceProvider.GetRequiredService<IUserService>();

            // Ensure database is created
            await context.Database.EnsureCreatedAsync();

            // Seed default admin user if no users exist
            await SeedDefaultUserAsync(userService);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Database initialization failed: {ex.Message}",
                "Database Error", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private async Task SeedDefaultUserAsync(IUserService userService)
    {
        try
        {
            var users = await userService.GetAllUsersAsync();
            if (!users.Any())
            {
                var adminUser = new CreateUserDto
                {
                    Username = "admin",
                    Email = "<EMAIL>",
                    FirstName = "System",
                    LastName = "Administrator",
                    Password = "admin123",
                    Role = UserRole.Admin,
                    IsActive = true
                };

                await userService.CreateUserAsync(adminUser);

                MessageBox.Show("Default admin user created!\n\nUsername: admin\nPassword: admin123",
                    "Database Seeded", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to seed default user: {ex.Message}",
                "Seeding Error", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Database - Using SQLite for development (change to MySQL in production)
                services.AddDbContext<InventoryBillingDbContext>(options =>
                    options.UseSqlite("Data Source=InventoryBilling.db"));

                // Repository Pattern
                services.AddScoped<IUnitOfWork, UnitOfWork>();

                // Services
                services.AddScoped<IUserService, UserService>();
                services.AddScoped<IAuthenticationService, AuthenticationService>();

                // ViewModels
                services.AddTransient<LoginViewModel>();
                services.AddTransient<MainViewModel>();

                // Views
                services.AddTransient<LoginWindow>();
                services.AddTransient<MainWindow>();
            });
    }


}

