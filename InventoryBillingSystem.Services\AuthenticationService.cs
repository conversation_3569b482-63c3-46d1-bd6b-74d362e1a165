using System.Security.Cryptography;
using System.Text;
using InventoryBillingSystem.Core.DTOs;
using InventoryBillingSystem.Core.Interfaces;
using InventoryBillingSystem.Core.Utilities;

namespace InventoryBillingSystem.Services
{
    public class AuthenticationService : IAuthenticationService
    {
        private readonly IUserService _userService;
        private UserDto? _currentUser;

        public AuthenticationService(IUserService userService)
        {
            _userService = userService;
        }

        public async Task<LoginResultDto> LoginAsync(LoginDto loginDto)
        {
            try
            {
                var user = await _userService.GetUserByUsernameAsync(loginDto.Username);
                
                if (user == null)
                {
                    return new LoginResultDto
                    {
                        Success = false,
                        Message = "Invalid username or password."
                    };
                }

                if (!user.IsActive)
                {
                    return new LoginResultDto
                    {
                        Success = false,
                        Message = "Your account has been deactivated. Please contact an administrator."
                    };
                }

                // For now, we'll implement a simple password verification
                // In a real application, you would verify against the hashed password
                var userEntity = await GetUserEntityByUsernameAsync(loginDto.Username);
                if (userEntity == null || !PasswordHasher.VerifyPassword(loginDto.Password, userEntity.PasswordHash))
                {
                    return new LoginResultDto
                    {
                        Success = false,
                        Message = "Invalid username or password."
                    };
                }

                _currentUser = user;
                
                // Update last login time
                await UpdateLastLoginAsync(user.Id);

                return new LoginResultDto
                {
                    Success = true,
                    Message = "Login successful.",
                    User = user
                };
            }
            catch (Exception ex)
            {
                return new LoginResultDto
                {
                    Success = false,
                    Message = $"An error occurred during login: {ex.Message}"
                };
            }
        }

        public async Task LogoutAsync()
        {
            _currentUser = null;
            await Task.CompletedTask;
        }

        public async Task<bool> ChangePasswordAsync(ChangePasswordDto changePasswordDto)
        {
            try
            {
                if (changePasswordDto.NewPassword != changePasswordDto.ConfirmPassword)
                {
                    return false;
                }

                var userEntity = await GetUserEntityByIdAsync(changePasswordDto.UserId);
                if (userEntity == null)
                {
                    return false;
                }

                if (!PasswordHasher.VerifyPassword(changePasswordDto.CurrentPassword, userEntity.PasswordHash))
                {
                    return false;
                }

                var newPasswordHash = PasswordHasher.HashPassword(changePasswordDto.NewPassword);
                await UpdateUserPasswordAsync(changePasswordDto.UserId, newPasswordHash);

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<UserDto?> GetCurrentUserAsync()
        {
            return await Task.FromResult(_currentUser);
        }

        public async Task<bool> IsUserLoggedInAsync()
        {
            return await Task.FromResult(_currentUser != null);
        }

        public string HashPassword(string password)
        {
            return PasswordHasher.HashPassword(password);
        }

        public bool VerifyPassword(string password, string hashedPassword)
        {
            return PasswordHasher.VerifyPassword(password, hashedPassword);
        }

        // These methods would need to be implemented to interact with the database
        // For now, they're placeholders
        private async Task<dynamic?> GetUserEntityByUsernameAsync(string username)
        {
            // This would use the repository to get the user entity with password hash
            await Task.CompletedTask;
            return null;
        }

        private async Task<dynamic?> GetUserEntityByIdAsync(int id)
        {
            // This would use the repository to get the user entity with password hash
            await Task.CompletedTask;
            return null;
        }

        private async Task UpdateLastLoginAsync(int userId)
        {
            // This would update the user's last login time
            await Task.CompletedTask;
        }

        private async Task UpdateUserPasswordAsync(int userId, string newPasswordHash)
        {
            // This would update the user's password hash
            await Task.CompletedTask;
        }
    }
}
