using System.Security.Cryptography;
using System.Text;

namespace InventoryBillingSystem.Core.Utilities
{
    public static class PasswordHasher
    {
        private const string Salt = "InventoryBillingSalt";

        public static string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + Salt));
            return Convert.ToBase64String(hashedBytes);
        }

        public static bool VerifyPassword(string password, string hashedPassword)
        {
            var hashOfInput = HashPassword(password);
            return hashOfInput.Equals(hashedPassword);
        }
    }
}
