{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.WPF\\InventoryBillingSystem.WPF.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Core\\InventoryBillingSystem.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Core\\InventoryBillingSystem.Core.csproj", "projectName": "InventoryBillingSystem.Core", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Core\\InventoryBillingSystem.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Data\\InventoryBillingSystem.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Data\\InventoryBillingSystem.Data.csproj", "projectName": "InventoryBillingSystem.Data", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Data\\InventoryBillingSystem.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Core\\InventoryBillingSystem.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Core\\InventoryBillingSystem.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[9.0.0-preview.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Services\\InventoryBillingSystem.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Services\\InventoryBillingSystem.Services.csproj", "projectName": "InventoryBillingSystem.Services", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Services\\InventoryBillingSystem.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Core\\InventoryBillingSystem.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Core\\InventoryBillingSystem.Core.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Data\\InventoryBillingSystem.Data.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Data\\InventoryBillingSystem.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.WPF\\InventoryBillingSystem.WPF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.WPF\\InventoryBillingSystem.WPF.csproj", "projectName": "InventoryBillingSystem.WPF", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.WPF\\InventoryBillingSystem.WPF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.WPF\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Core\\InventoryBillingSystem.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Core\\InventoryBillingSystem.Core.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Data\\InventoryBillingSystem.Data.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Data\\InventoryBillingSystem.Data.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Services\\InventoryBillingSystem.Services.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\InventoryWindowsApp\\InventoryBillingSystem.Services\\InventoryBillingSystem.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"MaterialDesignThemes": {"target": "Package", "version": "[5.2.1, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}