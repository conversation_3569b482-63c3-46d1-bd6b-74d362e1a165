﻿<Application x:Class="InventoryBillingSystem.WPF.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:InventoryBillingSystem.WPF.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Indigo" SecondaryColor="Cyan" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <local:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <local:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
            <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
