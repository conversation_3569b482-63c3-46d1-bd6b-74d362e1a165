﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InventoryBillingSystem.WPF", "InventoryBillingSystem.WPF\InventoryBillingSystem.WPF.csproj", "{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InventoryBillingSystem.Core", "InventoryBillingSystem.Core\InventoryBillingSystem.Core.csproj", "{16174D9B-4E7F-4FB7-A473-40AB962C40F4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InventoryBillingSystem.Data", "InventoryBillingSystem.Data\InventoryBillingSystem.Data.csproj", "{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InventoryBillingSystem.Services", "InventoryBillingSystem.Services\InventoryBillingSystem.Services.csproj", "{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InventoryBillingSystem.Tests", "InventoryBillingSystem.Tests\InventoryBillingSystem.Tests.csproj", "{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}.Debug|x64.Build.0 = Debug|Any CPU
		{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}.Debug|x86.Build.0 = Debug|Any CPU
		{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}.Release|Any CPU.Build.0 = Release|Any CPU
		{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}.Release|x64.ActiveCfg = Release|Any CPU
		{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}.Release|x64.Build.0 = Release|Any CPU
		{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}.Release|x86.ActiveCfg = Release|Any CPU
		{5B9EF2A7-5E75-416D-83A4-5C98C32537F0}.Release|x86.Build.0 = Release|Any CPU
		{16174D9B-4E7F-4FB7-A473-40AB962C40F4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{16174D9B-4E7F-4FB7-A473-40AB962C40F4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{16174D9B-4E7F-4FB7-A473-40AB962C40F4}.Debug|x64.ActiveCfg = Debug|Any CPU
		{16174D9B-4E7F-4FB7-A473-40AB962C40F4}.Debug|x64.Build.0 = Debug|Any CPU
		{16174D9B-4E7F-4FB7-A473-40AB962C40F4}.Debug|x86.ActiveCfg = Debug|Any CPU
		{16174D9B-4E7F-4FB7-A473-40AB962C40F4}.Debug|x86.Build.0 = Debug|Any CPU
		{16174D9B-4E7F-4FB7-A473-40AB962C40F4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{16174D9B-4E7F-4FB7-A473-40AB962C40F4}.Release|Any CPU.Build.0 = Release|Any CPU
		{16174D9B-4E7F-4FB7-A473-40AB962C40F4}.Release|x64.ActiveCfg = Release|Any CPU
		{16174D9B-4E7F-4FB7-A473-40AB962C40F4}.Release|x64.Build.0 = Release|Any CPU
		{16174D9B-4E7F-4FB7-A473-40AB962C40F4}.Release|x86.ActiveCfg = Release|Any CPU
		{16174D9B-4E7F-4FB7-A473-40AB962C40F4}.Release|x86.Build.0 = Release|Any CPU
		{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}.Debug|x64.Build.0 = Debug|Any CPU
		{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}.Debug|x86.Build.0 = Debug|Any CPU
		{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}.Release|Any CPU.Build.0 = Release|Any CPU
		{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}.Release|x64.ActiveCfg = Release|Any CPU
		{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}.Release|x64.Build.0 = Release|Any CPU
		{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}.Release|x86.ActiveCfg = Release|Any CPU
		{9142AA6F-64EA-4BF2-899D-64CD9FD39E7D}.Release|x86.Build.0 = Release|Any CPU
		{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}.Debug|x64.Build.0 = Debug|Any CPU
		{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}.Debug|x86.Build.0 = Debug|Any CPU
		{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}.Release|x64.ActiveCfg = Release|Any CPU
		{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}.Release|x64.Build.0 = Release|Any CPU
		{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}.Release|x86.ActiveCfg = Release|Any CPU
		{EA23F5E1-FEAE-462C-9B59-CAB2713B5B87}.Release|x86.Build.0 = Release|Any CPU
		{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}.Debug|x64.Build.0 = Debug|Any CPU
		{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}.Debug|x86.Build.0 = Debug|Any CPU
		{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}.Release|Any CPU.Build.0 = Release|Any CPU
		{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}.Release|x64.ActiveCfg = Release|Any CPU
		{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}.Release|x64.Build.0 = Release|Any CPU
		{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}.Release|x86.ActiveCfg = Release|Any CPU
		{4EE8FFDF-CFA5-4F35-B50E-1DAA50723E1E}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
