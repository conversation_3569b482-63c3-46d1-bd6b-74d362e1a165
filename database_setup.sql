-- =====================================================
-- Inventory Billing System - Complete MySQL Database Setup
-- =====================================================

-- Create database
DROP DATABASE IF EXISTS InventoryBillingDB;
CREATE DATABASE InventoryBillingDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE InventoryBillingDB;

-- =====================================================
-- 1. USERS TABLE
-- =====================================================
CREATE TABLE Users (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Username VARCHAR(100) NOT NULL UNIQUE,
    Email VARCHAR(255) NOT NULL UNIQUE,
    FirstName VARCHAR(100) NOT NULL,
    LastName VARCHAR(100) NOT NULL,
    PasswordHash TEXT NOT NULL,
    PhoneNumber VARCHAR(20),
    Role INT NOT NULL DEFAULT 2, -- 0=Admin, 1=Manager, 2=Employee
    IsActive BOOLEAN NOT NULL DEFAULT TRUE,
    LastLoginAt DATETIME(6),
    ProfileImagePath TEXT,
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    UpdatedAt DATETIME(6),
    CreatedBy TEXT,
    UpdatedBy TEXT,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    DeletedAt DATETIME(6),
    DeletedBy TEXT,
    
    INDEX idx_username (Username),
    INDEX idx_email (Email),
    INDEX idx_role (Role),
    INDEX idx_active (IsActive),
    INDEX idx_deleted (IsDeleted)
);

-- =====================================================
-- 2. CATEGORIES TABLE
-- =====================================================
CREATE TABLE Categories (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Name VARCHAR(200) NOT NULL,
    Code VARCHAR(50) NOT NULL UNIQUE,
    Description TEXT,
    ParentCategoryId INT,
    IsActive BOOLEAN NOT NULL DEFAULT TRUE,
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    UpdatedAt DATETIME(6),
    CreatedBy TEXT,
    UpdatedBy TEXT,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    DeletedAt DATETIME(6),
    DeletedBy TEXT,
    
    FOREIGN KEY (ParentCategoryId) REFERENCES Categories(Id) ON DELETE SET NULL,
    INDEX idx_code (Code),
    INDEX idx_parent (ParentCategoryId),
    INDEX idx_active (IsActive),
    INDEX idx_deleted (IsDeleted)
);

-- =====================================================
-- 3. SUPPLIERS TABLE
-- =====================================================
CREATE TABLE Suppliers (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Name VARCHAR(200) NOT NULL,
    ContactPerson VARCHAR(100),
    Email VARCHAR(255),
    Phone VARCHAR(20),
    Address TEXT,
    City VARCHAR(100),
    State VARCHAR(100),
    Country VARCHAR(100),
    PostalCode VARCHAR(20),
    TaxNumber VARCHAR(50),
    PaymentTerms TEXT,
    IsActive BOOLEAN NOT NULL DEFAULT TRUE,
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    UpdatedAt DATETIME(6),
    CreatedBy TEXT,
    UpdatedBy TEXT,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    DeletedAt DATETIME(6),
    DeletedBy TEXT,
    
    INDEX idx_name (Name),
    INDEX idx_email (Email),
    INDEX idx_active (IsActive),
    INDEX idx_deleted (IsDeleted)
);

-- =====================================================
-- 4. PRODUCTS TABLE
-- =====================================================
CREATE TABLE Products (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Name VARCHAR(200) NOT NULL,
    Code VARCHAR(100) NOT NULL UNIQUE,
    Description TEXT,
    CategoryId INT NOT NULL,
    SupplierId INT,
    UnitOfMeasure VARCHAR(50) NOT NULL,
    MinimumStockLevel INT NOT NULL DEFAULT 0,
    MaximumStockLevel INT,
    ReorderPoint INT NOT NULL DEFAULT 0,
    IsActive BOOLEAN NOT NULL DEFAULT TRUE,
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    UpdatedAt DATETIME(6),
    CreatedBy TEXT,
    UpdatedBy TEXT,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    DeletedAt DATETIME(6),
    DeletedBy TEXT,
    
    FOREIGN KEY (CategoryId) REFERENCES Categories(Id) ON DELETE RESTRICT,
    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id) ON DELETE SET NULL,
    INDEX idx_code (Code),
    INDEX idx_name (Name),
    INDEX idx_category (CategoryId),
    INDEX idx_supplier (SupplierId),
    INDEX idx_active (IsActive),
    INDEX idx_deleted (IsDeleted)
);

-- =====================================================
-- 5. PRODUCT VARIANTS TABLE
-- =====================================================
CREATE TABLE ProductVariants (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    ProductId INT NOT NULL,
    SKU VARCHAR(100) NOT NULL UNIQUE,
    VariantName VARCHAR(200),
    Size VARCHAR(50),
    Color VARCHAR(50),
    Weight DECIMAL(10,3),
    Dimensions VARCHAR(100),
    PurchasePrice DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    SellingPrice DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    CurrentStock INT NOT NULL DEFAULT 0,
    ReservedStock INT NOT NULL DEFAULT 0,
    AvailableStock INT GENERATED ALWAYS AS (CurrentStock - ReservedStock) STORED,
    IsActive BOOLEAN NOT NULL DEFAULT TRUE,
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    UpdatedAt DATETIME(6),
    CreatedBy TEXT,
    UpdatedBy TEXT,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    DeletedAt DATETIME(6),
    DeletedBy TEXT,
    
    FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE CASCADE,
    INDEX idx_sku (SKU),
    INDEX idx_product (ProductId),
    INDEX idx_stock (CurrentStock),
    INDEX idx_active (IsActive),
    INDEX idx_deleted (IsDeleted)
);

-- =====================================================
-- 6. CUSTOMERS TABLE
-- =====================================================
CREATE TABLE Customers (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Name VARCHAR(200) NOT NULL,
    Email VARCHAR(255) UNIQUE,
    Phone VARCHAR(20),
    Address TEXT,
    City VARCHAR(100),
    State VARCHAR(100),
    Country VARCHAR(100),
    PostalCode VARCHAR(20),
    TaxNumber VARCHAR(50),
    CreditLimit DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    OutstandingBalance DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    PaymentTerms TEXT,
    IsActive BOOLEAN NOT NULL DEFAULT TRUE,
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    UpdatedAt DATETIME(6),
    CreatedBy TEXT,
    UpdatedBy TEXT,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    DeletedAt DATETIME(6),
    DeletedBy TEXT,

    INDEX idx_name (Name),
    INDEX idx_email (Email),
    INDEX idx_active (IsActive),
    INDEX idx_deleted (IsDeleted)
);

-- =====================================================
-- 7. INVOICES TABLE
-- =====================================================
CREATE TABLE Invoices (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    InvoiceNumber VARCHAR(50) NOT NULL UNIQUE,
    CustomerId INT NOT NULL,
    InvoiceDate DATETIME(6) NOT NULL,
    DueDate DATETIME(6) NOT NULL,
    SubTotal DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    TaxAmount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    DiscountAmount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    TotalAmount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    PaidAmount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    BalanceAmount DECIMAL(18,2) GENERATED ALWAYS AS (TotalAmount - PaidAmount) STORED,
    Status INT NOT NULL DEFAULT 0, -- 0=Draft, 1=Sent, 2=Paid, 3=Overdue, 4=Cancelled
    Notes TEXT,
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    UpdatedAt DATETIME(6),
    CreatedBy TEXT,
    UpdatedBy TEXT,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    DeletedAt DATETIME(6),
    DeletedBy TEXT,

    FOREIGN KEY (CustomerId) REFERENCES Customers(Id) ON DELETE RESTRICT,
    INDEX idx_invoice_number (InvoiceNumber),
    INDEX idx_customer (CustomerId),
    INDEX idx_date (InvoiceDate),
    INDEX idx_status (Status),
    INDEX idx_deleted (IsDeleted)
);

-- =====================================================
-- 8. INVOICE ITEMS TABLE
-- =====================================================
CREATE TABLE InvoiceItems (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    InvoiceId INT NOT NULL,
    ProductVariantId INT NOT NULL,
    Quantity INT NOT NULL,
    UnitPrice DECIMAL(18,2) NOT NULL,
    DiscountPercentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    DiscountAmount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    TaxPercentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    TaxAmount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    LineTotal DECIMAL(18,2) GENERATED ALWAYS AS ((Quantity * UnitPrice) - DiscountAmount + TaxAmount) STORED,
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    UpdatedAt DATETIME(6),
    CreatedBy TEXT,
    UpdatedBy TEXT,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    DeletedAt DATETIME(6),
    DeletedBy TEXT,

    FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id) ON DELETE CASCADE,
    FOREIGN KEY (ProductVariantId) REFERENCES ProductVariants(Id) ON DELETE RESTRICT,
    INDEX idx_invoice (InvoiceId),
    INDEX idx_product_variant (ProductVariantId),
    INDEX idx_deleted (IsDeleted)
);

-- =====================================================
-- 9. PAYMENTS TABLE
-- =====================================================
CREATE TABLE Payments (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    InvoiceId INT NOT NULL,
    PaymentDate DATETIME(6) NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    PaymentMethod INT NOT NULL DEFAULT 0, -- 0=Cash, 1=Card, 2=BankTransfer, 3=Check, 4=Other
    ReferenceNumber VARCHAR(100),
    Notes TEXT,
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    UpdatedAt DATETIME(6),
    CreatedBy TEXT,
    UpdatedBy TEXT,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    DeletedAt DATETIME(6),
    DeletedBy TEXT,

    FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id) ON DELETE RESTRICT,
    INDEX idx_invoice (InvoiceId),
    INDEX idx_payment_date (PaymentDate),
    INDEX idx_payment_method (PaymentMethod),
    INDEX idx_deleted (IsDeleted)
);

-- =====================================================
-- 10. PURCHASE ORDERS TABLE
-- =====================================================
CREATE TABLE PurchaseOrders (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    OrderNumber VARCHAR(50) NOT NULL UNIQUE,
    SupplierId INT NOT NULL,
    OrderDate DATETIME(6) NOT NULL,
    ExpectedDeliveryDate DATETIME(6),
    ActualDeliveryDate DATETIME(6),
    SubTotal DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    TaxAmount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    DiscountAmount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    TotalAmount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    Status INT NOT NULL DEFAULT 0, -- 0=Draft, 1=Sent, 2=Confirmed, 3=PartiallyReceived, 4=Received, 5=Cancelled
    Notes TEXT,
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    UpdatedAt DATETIME(6),
    CreatedBy TEXT,
    UpdatedBy TEXT,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    DeletedAt DATETIME(6),
    DeletedBy TEXT,

    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id) ON DELETE RESTRICT,
    INDEX idx_order_number (OrderNumber),
    INDEX idx_supplier (SupplierId),
    INDEX idx_order_date (OrderDate),
    INDEX idx_status (Status),
    INDEX idx_deleted (IsDeleted)
);

-- =====================================================
-- 11. PURCHASE ORDER ITEMS TABLE
-- =====================================================
CREATE TABLE PurchaseOrderItems (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    PurchaseOrderId INT NOT NULL,
    ProductVariantId INT NOT NULL,
    OrderedQuantity INT NOT NULL,
    ReceivedQuantity INT NOT NULL DEFAULT 0,
    UnitPrice DECIMAL(18,2) NOT NULL,
    DiscountPercentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    DiscountAmount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    TaxPercentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    TaxAmount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    LineTotal DECIMAL(18,2) GENERATED ALWAYS AS ((OrderedQuantity * UnitPrice) - DiscountAmount + TaxAmount) STORED,
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    UpdatedAt DATETIME(6),
    CreatedBy TEXT,
    UpdatedBy TEXT,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    DeletedAt DATETIME(6),
    DeletedBy TEXT,

    FOREIGN KEY (PurchaseOrderId) REFERENCES PurchaseOrders(Id) ON DELETE CASCADE,
    FOREIGN KEY (ProductVariantId) REFERENCES ProductVariants(Id) ON DELETE RESTRICT,
    INDEX idx_purchase_order (PurchaseOrderId),
    INDEX idx_product_variant (ProductVariantId),
    INDEX idx_deleted (IsDeleted)
);

-- =====================================================
-- 12. STOCK MOVEMENTS TABLE
-- =====================================================
CREATE TABLE StockMovements (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    ProductVariantId INT NOT NULL,
    MovementType INT NOT NULL, -- 0=Purchase, 1=Sale, 2=Adjustment, 3=Transfer, 4=Return
    Quantity INT NOT NULL,
    UnitPrice DECIMAL(18,2),
    ReferenceType INT, -- 0=Invoice, 1=PurchaseOrder, 2=Adjustment, 3=Transfer
    ReferenceId INT,
    Notes TEXT,
    MovementDate DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    UpdatedAt DATETIME(6),
    CreatedBy TEXT,
    UpdatedBy TEXT,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    DeletedAt DATETIME(6),
    DeletedBy TEXT,

    FOREIGN KEY (ProductVariantId) REFERENCES ProductVariants(Id) ON DELETE RESTRICT,
    INDEX idx_product_variant (ProductVariantId),
    INDEX idx_movement_type (MovementType),
    INDEX idx_movement_date (MovementDate),
    INDEX idx_reference (ReferenceType, ReferenceId),
    INDEX idx_deleted (IsDeleted)
);

-- =====================================================
-- 13. AUDIT LOGS TABLE
-- =====================================================
CREATE TABLE AuditLogs (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    UserId INT,
    Action VARCHAR(100) NOT NULL,
    TableName VARCHAR(100) NOT NULL,
    RecordId INT,
    OldValues JSON,
    NewValues JSON,
    IpAddress VARCHAR(45),
    UserAgent TEXT,
    CreatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),

    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE SET NULL,
    INDEX idx_user (UserId),
    INDEX idx_action (Action),
    INDEX idx_table (TableName),
    INDEX idx_record (RecordId),
    INDEX idx_created_at (CreatedAt)
);

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert default admin user (password: admin123)
INSERT INTO Users (Username, Email, FirstName, LastName, PasswordHash, Role, IsActive, CreatedAt) VALUES
('admin', '<EMAIL>', 'System', 'Administrator', 'jGl25bVBBBW96Qi9Te4V37Fnqchz/Eu4qB9vKrRIqRg=', 0, TRUE, NOW(6)),
('manager', '<EMAIL>', 'John', 'Manager', 'jGl25bVBBBW96Qi9Te4V37Fnqchz/Eu4qB9vKrRIqRg=', 1, TRUE, NOW(6)),
('employee', '<EMAIL>', 'Jane', 'Employee', 'jGl25bVBBBW96Qi9Te4V37Fnqchz/Eu4qB9vKrRIqRg=', 2, TRUE, NOW(6));

-- Insert sample categories
INSERT INTO Categories (Name, Code, Description, IsActive, CreatedAt) VALUES
('Electronics', 'ELEC', 'Electronic devices and components', TRUE, NOW(6)),
('Computers', 'COMP', 'Computer hardware and accessories', TRUE, NOW(6)),
('Mobile Devices', 'MOBILE', 'Smartphones, tablets, and accessories', TRUE, NOW(6)),
('Office Supplies', 'OFFICE', 'General office supplies and stationery', TRUE, NOW(6)),
('Furniture', 'FURN', 'Office and home furniture', TRUE, NOW(6));

-- Insert sample suppliers
INSERT INTO Suppliers (Name, ContactPerson, Email, Phone, Address, City, Country, IsActive, CreatedAt) VALUES
('TechCorp Solutions', 'Mike Johnson', '<EMAIL>', '******-0101', '123 Tech Street', 'San Francisco', 'USA', TRUE, NOW(6)),
('Global Electronics Ltd', 'Sarah Chen', '<EMAIL>', '******-0102', '456 Electronics Ave', 'New York', 'USA', TRUE, NOW(6)),
('Office Pro Supplies', 'David Wilson', '<EMAIL>', '******-0103', '789 Supply Road', 'Chicago', 'USA', TRUE, NOW(6));

-- Insert sample customers
INSERT INTO Customers (Name, Email, Phone, Address, City, Country, CreditLimit, IsActive, CreatedAt) VALUES
('ABC Corporation', '<EMAIL>', '******-1001', '100 Business Blvd', 'Los Angeles', 'USA', 50000.00, TRUE, NOW(6)),
('XYZ Enterprises', '<EMAIL>', '******-1002', '200 Commerce St', 'Miami', 'USA', 25000.00, TRUE, NOW(6)),
('Tech Startup Inc', '<EMAIL>', '******-1003', '300 Innovation Dr', 'Austin', 'USA', 15000.00, TRUE, NOW(6));

-- Insert sample products
INSERT INTO Products (Name, Code, Description, CategoryId, SupplierId, UnitOfMeasure, MinimumStockLevel, ReorderPoint, IsActive, CreatedAt) VALUES
('Laptop Computer', 'LAP001', 'High-performance business laptop', 2, 1, 'Each', 5, 10, TRUE, NOW(6)),
('Wireless Mouse', 'MOU001', 'Ergonomic wireless mouse', 2, 1, 'Each', 20, 30, TRUE, NOW(6)),
('Office Chair', 'CHR001', 'Ergonomic office chair with lumbar support', 5, 3, 'Each', 3, 5, TRUE, NOW(6)),
('Smartphone', 'PHN001', 'Latest model smartphone', 3, 2, 'Each', 10, 15, TRUE, NOW(6)),
('Printer Paper', 'PAP001', 'A4 white printer paper', 4, 3, 'Ream', 50, 100, TRUE, NOW(6));

-- Insert sample product variants
INSERT INTO ProductVariants (ProductId, SKU, VariantName, PurchasePrice, SellingPrice, CurrentStock, IsActive, CreatedAt) VALUES
(1, 'LAP001-BLK', 'Laptop - Black', 800.00, 1200.00, 15, TRUE, NOW(6)),
(1, 'LAP001-SLV', 'Laptop - Silver', 800.00, 1200.00, 12, TRUE, NOW(6)),
(2, 'MOU001-BLK', 'Wireless Mouse - Black', 15.00, 25.00, 50, TRUE, NOW(6)),
(2, 'MOU001-WHT', 'Wireless Mouse - White', 15.00, 25.00, 45, TRUE, NOW(6)),
(3, 'CHR001-BLK', 'Office Chair - Black', 150.00, 250.00, 8, TRUE, NOW(6)),
(4, 'PHN001-BLK', 'Smartphone - Black', 400.00, 600.00, 25, TRUE, NOW(6)),
(4, 'PHN001-BLU', 'Smartphone - Blue', 400.00, 600.00, 20, TRUE, NOW(6)),
(5, 'PAP001-A4', 'A4 Printer Paper - 500 sheets', 3.50, 6.00, 200, TRUE, NOW(6));

-- Insert sample invoices
INSERT INTO Invoices (InvoiceNumber, CustomerId, InvoiceDate, DueDate, SubTotal, TaxAmount, TotalAmount, Status, CreatedAt) VALUES
('INV-2024-001', 1, '2024-01-15 10:00:00', '2024-02-14 23:59:59', 1250.00, 125.00, 1375.00, 1, NOW(6)),
('INV-2024-002', 2, '2024-01-20 14:30:00', '2024-02-19 23:59:59', 75.00, 7.50, 82.50, 2, NOW(6)),
('INV-2024-003', 3, '2024-01-25 09:15:00', '2024-02-24 23:59:59', 850.00, 85.00, 935.00, 0, NOW(6));

-- Insert sample invoice items
INSERT INTO InvoiceItems (InvoiceId, ProductVariantId, Quantity, UnitPrice, TaxPercentage, TaxAmount, CreatedAt) VALUES
(1, 1, 1, 1200.00, 10.00, 120.00, NOW(6)),
(1, 3, 2, 25.00, 10.00, 5.00, NOW(6)),
(2, 4, 3, 25.00, 10.00, 7.50, NOW(6)),
(3, 6, 1, 600.00, 10.00, 60.00, NOW(6)),
(3, 5, 1, 250.00, 10.00, 25.00, NOW(6));

-- Insert sample payments
INSERT INTO Payments (InvoiceId, PaymentDate, Amount, PaymentMethod, ReferenceNumber, CreatedAt) VALUES
(2, '2024-01-22 16:45:00', 82.50, 1, 'CC-*********', NOW(6));

-- Insert sample purchase orders
INSERT INTO PurchaseOrders (OrderNumber, SupplierId, OrderDate, SubTotal, TaxAmount, TotalAmount, Status, CreatedAt) VALUES
('PO-2024-001', 1, '2024-01-10 11:00:00', 8000.00, 800.00, 8800.00, 2, NOW(6)),
('PO-2024-002', 2, '2024-01-12 15:30:00', 4000.00, 400.00, 4400.00, 1, NOW(6));

-- Insert sample purchase order items
INSERT INTO PurchaseOrderItems (PurchaseOrderId, ProductVariantId, OrderedQuantity, ReceivedQuantity, UnitPrice, TaxPercentage, TaxAmount, CreatedAt) VALUES
(1, 1, 10, 10, 800.00, 10.00, 800.00, NOW(6)),
(2, 6, 10, 0, 400.00, 10.00, 400.00, NOW(6));

-- =====================================================
-- VIEWS FOR REPORTING
-- =====================================================

-- Product inventory view
CREATE VIEW ProductInventoryView AS
SELECT
    p.Id AS ProductId,
    p.Name AS ProductName,
    p.Code AS ProductCode,
    c.Name AS CategoryName,
    s.Name AS SupplierName,
    pv.Id AS VariantId,
    pv.SKU,
    pv.VariantName,
    pv.PurchasePrice,
    pv.SellingPrice,
    pv.CurrentStock,
    pv.ReservedStock,
    pv.AvailableStock,
    p.MinimumStockLevel,
    p.ReorderPoint,
    CASE
        WHEN pv.CurrentStock <= p.ReorderPoint THEN 'Reorder Required'
        WHEN pv.CurrentStock <= p.MinimumStockLevel THEN 'Low Stock'
        ELSE 'In Stock'
    END AS StockStatus
FROM Products p
JOIN Categories c ON p.CategoryId = c.Id
LEFT JOIN Suppliers s ON p.SupplierId = s.Id
JOIN ProductVariants pv ON p.Id = pv.ProductId
WHERE p.IsDeleted = FALSE AND pv.IsDeleted = FALSE;

-- Invoice summary view
CREATE VIEW InvoiceSummaryView AS
SELECT
    i.Id AS InvoiceId,
    i.InvoiceNumber,
    c.Name AS CustomerName,
    i.InvoiceDate,
    i.DueDate,
    i.TotalAmount,
    i.PaidAmount,
    i.BalanceAmount,
    CASE i.Status
        WHEN 0 THEN 'Draft'
        WHEN 1 THEN 'Sent'
        WHEN 2 THEN 'Paid'
        WHEN 3 THEN 'Overdue'
        WHEN 4 THEN 'Cancelled'
    END AS StatusText,
    CASE
        WHEN i.Status = 2 THEN 'Paid'
        WHEN i.DueDate < NOW() AND i.BalanceAmount > 0 THEN 'Overdue'
        WHEN i.DueDate >= NOW() AND i.BalanceAmount > 0 THEN 'Outstanding'
        ELSE 'Unknown'
    END AS PaymentStatus
FROM Invoices i
JOIN Customers c ON i.CustomerId = c.Id
WHERE i.IsDeleted = FALSE;

-- =====================================================
-- STORED PROCEDURES
-- =====================================================

DELIMITER //

-- Procedure to update stock after sale
CREATE PROCEDURE UpdateStockAfterSale(
    IN p_ProductVariantId INT,
    IN p_Quantity INT,
    IN p_InvoiceId INT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- Update product variant stock
    UPDATE ProductVariants
    SET CurrentStock = CurrentStock - p_Quantity,
        UpdatedAt = NOW(6)
    WHERE Id = p_ProductVariantId;

    -- Insert stock movement record
    INSERT INTO StockMovements (ProductVariantId, MovementType, Quantity, ReferenceType, ReferenceId, MovementDate, CreatedAt)
    VALUES (p_ProductVariantId, 1, -p_Quantity, 0, p_InvoiceId, NOW(6), NOW(6));

    COMMIT;
END //

-- Procedure to update stock after purchase
CREATE PROCEDURE UpdateStockAfterPurchase(
    IN p_ProductVariantId INT,
    IN p_Quantity INT,
    IN p_UnitPrice DECIMAL(18,2),
    IN p_PurchaseOrderId INT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- Update product variant stock
    UPDATE ProductVariants
    SET CurrentStock = CurrentStock + p_Quantity,
        PurchasePrice = p_UnitPrice,
        UpdatedAt = NOW(6)
    WHERE Id = p_ProductVariantId;

    -- Insert stock movement record
    INSERT INTO StockMovements (ProductVariantId, MovementType, Quantity, UnitPrice, ReferenceType, ReferenceId, MovementDate, CreatedAt)
    VALUES (p_ProductVariantId, 0, p_Quantity, p_UnitPrice, 1, p_PurchaseOrderId, NOW(6), NOW(6));

    COMMIT;
END //

DELIMITER ;

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Trigger to update invoice totals when invoice items change
DELIMITER //
CREATE TRIGGER UpdateInvoiceTotals AFTER INSERT ON InvoiceItems
FOR EACH ROW
BEGIN
    UPDATE Invoices
    SET SubTotal = (
        SELECT COALESCE(SUM((Quantity * UnitPrice) - DiscountAmount), 0)
        FROM InvoiceItems
        WHERE InvoiceId = NEW.InvoiceId AND IsDeleted = FALSE
    ),
    TaxAmount = (
        SELECT COALESCE(SUM(TaxAmount), 0)
        FROM InvoiceItems
        WHERE InvoiceId = NEW.InvoiceId AND IsDeleted = FALSE
    ),
    UpdatedAt = NOW(6)
    WHERE Id = NEW.InvoiceId;

    UPDATE Invoices
    SET TotalAmount = SubTotal + TaxAmount - DiscountAmount
    WHERE Id = NEW.InvoiceId;
END //
DELIMITER ;

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Additional performance indexes
CREATE INDEX idx_products_category_supplier ON Products(CategoryId, SupplierId);
CREATE INDEX idx_product_variants_stock ON ProductVariants(CurrentStock, ReorderPoint);
CREATE INDEX idx_invoices_date_status ON Invoices(InvoiceDate, Status);
CREATE INDEX idx_invoice_items_product ON InvoiceItems(ProductVariantId, InvoiceId);
CREATE INDEX idx_stock_movements_date_type ON StockMovements(MovementDate, MovementType);

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT 'Database setup completed successfully!' AS Message,
       'Default login credentials:' AS Info,
       'Username: admin, Password: admin123' AS AdminLogin,
       'Username: manager, Password: admin123' AS ManagerLogin,
       'Username: employee, Password: admin123' AS EmployeeLogin;
