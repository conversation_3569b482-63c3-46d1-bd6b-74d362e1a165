﻿<Window x:Class="InventoryBillingSystem.WPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:System="clr-namespace:System;assembly=mscorlib"
        Title="Inventory Billing System"
        Height="800"
        Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0"
                Background="{StaticResource PrimaryGradientBrush}"
                materialDesign:ElevationAssist.Elevation="Dp6">
            <Grid Margin="24,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Border Background="White"
                            CornerRadius="20"
                            Width="40"
                            Height="40"
                            materialDesign:ElevationAssist.Elevation="Dp2">
                        <materialDesign:PackIcon Kind="Store"
                                               Width="24"
                                               Height="24"
                                               Foreground="{StaticResource PrimaryGradientBrush}"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel Margin="16,0,0,0" VerticalAlignment="Center">
                        <TextBlock Text="Inventory Billing System"
                                  Style="{StaticResource HeaderTextStyle}"
                                  FontSize="20"
                                  FontWeight="SemiBold"/>
                        <TextBlock Text="Professional Business Management"
                                  Foreground="White"
                                  FontSize="11"
                                  Opacity="0.8"/>
                    </StackPanel>
                </StackPanel>

                <!-- Search Bar -->
                <materialDesign:Card Grid.Column="1"
                                     Background="White"
                                     Opacity="0.9"
                                     Margin="40,0"
                                     VerticalAlignment="Center"
                                     Padding="0"
                                     materialDesign:ElevationAssist.Elevation="Dp2">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <materialDesign:PackIcon Grid.Column="0"
                                               Kind="Magnify"
                                               Margin="12,0,8,0"
                                               VerticalAlignment="Center"
                                               Opacity="0.6"/>
                        <TextBox Grid.Column="1"
                                materialDesign:HintAssist.Hint="Search products, customers, invoices..."
                                Style="{StaticResource MaterialDesignTextBox}"
                                BorderThickness="0"
                                FontSize="13"
                                VerticalAlignment="Center"/>
                        <Button Grid.Column="2"
                               Style="{StaticResource MaterialDesignIconButton}"
                               Width="32"
                               Height="32"
                               Padding="4">
                            <materialDesign:PackIcon Kind="FilterVariant" Width="16" Height="16"/>
                        </Button>
                    </Grid>
                </materialDesign:Card>

                <!-- User Menu -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <!-- Notifications -->
                    <Button Style="{StaticResource MaterialDesignIconForegroundButton}"
                           Width="40"
                           Height="40"
                           Margin="8,0">
                        <materialDesign:PackIcon Kind="Bell"
                                               Width="20"
                                               Height="20"
                                               Foreground="White"/>
                        <materialDesign:BadgedAssist.Badge>
                            <Border Background="{StaticResource AccentGradientBrush}"
                                   CornerRadius="8"
                                   MinWidth="16"
                                   Height="16">
                                <TextBlock Text="3"
                                          Foreground="White"
                                          FontSize="10"
                                          FontWeight="Bold"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"/>
                            </Border>
                        </materialDesign:BadgedAssist.Badge>
                    </Button>

                    <!-- User Profile -->
                    <materialDesign:PopupBox PlacementMode="BottomAndAlignRightEdges"
                                           StaysOpen="False">
                        <materialDesign:PopupBox.ToggleContent>
                            <StackPanel Orientation="Horizontal">
                                <Border Background="White"
                                       CornerRadius="20"
                                       Width="40"
                                       Height="40"
                                       materialDesign:ElevationAssist.Elevation="Dp2">
                                    <materialDesign:PackIcon Kind="Account"
                                                           Width="24"
                                                           Height="24"
                                                           Foreground="{StaticResource PrimaryGradientBrush}"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel Margin="12,0,8,0" VerticalAlignment="Center">
                                    <TextBlock Text="{Binding CurrentUser.FullName}"
                                              Foreground="White"
                                              FontSize="13"
                                              FontWeight="Medium"/>
                                    <TextBlock Text="{Binding CurrentUser.Role}"
                                              Foreground="White"
                                              FontSize="11"
                                              Opacity="0.8"/>
                                </StackPanel>
                                <materialDesign:PackIcon Kind="ChevronDown"
                                                       Width="16"
                                                       Height="16"
                                                       Foreground="White"
                                                       VerticalAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:PopupBox.ToggleContent>
                        <StackPanel>
                            <Button Content="Profile Settings"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Left"/>
                            <Button Content="Change Password"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Left"/>
                            <Separator/>
                            <Button Content="Logout"
                                   Command="{Binding LogoutCommand}"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Left"
                                   Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                        </StackPanel>
                    </materialDesign:PopupBox>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="280"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Navigation Menu -->
            <Border Grid.Column="0"
                    Background="{DynamicResource MaterialDesignCardBackground}"
                    materialDesign:ElevationAssist.Elevation="Dp4">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16,24,16,16">

                        <!-- Navigation Header -->
                        <TextBlock Text="NAVIGATION"
                                  Style="{StaticResource SubHeaderTextStyle}"
                                  FontSize="12"
                                  FontWeight="Bold"
                                  Opacity="0.6"
                                  Margin="16,0,0,16"/>

                        <!-- Dashboard -->
                        <materialDesign:Card Style="{StaticResource ModernCardStyle}"
                                           Margin="0,0,0,8">
                            <Button Command="{Binding NavigateCommand}"
                                   CommandParameter="Dashboard"
                                   Style="{StaticResource NavigationButtonStyle}">
                                <StackPanel Orientation="Horizontal">
                                    <Border Background="{StaticResource PrimaryGradientBrush}"
                                           CornerRadius="8"
                                           Width="32"
                                           Height="32">
                                        <materialDesign:PackIcon Kind="ViewDashboard"
                                                               Width="18"
                                                               Height="18"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel Margin="16,0,0,0" VerticalAlignment="Center">
                                        <TextBlock Text="Dashboard"
                                                  FontWeight="Medium"
                                                  FontSize="14"/>
                                        <TextBlock Text="Overview and Analytics"
                                                  FontSize="11"
                                                  Opacity="0.6"/>
                                    </StackPanel>
                                </StackPanel>
                            </Button>
                        </materialDesign:Card>

                        <!-- Customers -->
                        <materialDesign:Card Style="{StaticResource ModernCardStyle}"
                                           Margin="0,0,0,8">
                            <Button Command="{Binding NavigateCommand}"
                                   CommandParameter="Customers"
                                   Style="{StaticResource NavigationButtonStyle}">
                                <StackPanel Orientation="Horizontal">
                                    <Border Background="{StaticResource AccentGradientBrush}"
                                           CornerRadius="8"
                                           Width="32"
                                           Height="32">
                                        <materialDesign:PackIcon Kind="AccountGroup"
                                                               Width="18"
                                                               Height="18"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel Margin="16,0,0,0" VerticalAlignment="Center">
                                        <TextBlock Text="Customers"
                                                  FontWeight="Medium"
                                                  FontSize="14"/>
                                        <TextBlock Text="Manage Customer Data"
                                                  FontSize="11"
                                                  Opacity="0.6"/>
                                    </StackPanel>
                                </StackPanel>
                            </Button>
                        </materialDesign:Card>

                        <!-- Products -->
                        <materialDesign:Card Style="{StaticResource ModernCardStyle}"
                                           Margin="0,0,0,8">
                            <Button Command="{Binding NavigateCommand}"
                                   CommandParameter="Products"
                                   Style="{StaticResource NavigationButtonStyle}">
                                <StackPanel Orientation="Horizontal">
                                    <Border Background="#FF9800"
                                           CornerRadius="8"
                                           Width="32"
                                           Height="32">
                                        <materialDesign:PackIcon Kind="Package"
                                                               Width="18"
                                                               Height="18"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel Margin="16,0,0,0" VerticalAlignment="Center">
                                        <TextBlock Text="Products"
                                                  FontWeight="Medium"
                                                  FontSize="14"/>
                                        <TextBlock Text="Product Catalog"
                                                  FontSize="11"
                                                  Opacity="0.6"/>
                                    </StackPanel>
                                </StackPanel>
                            </Button>
                        </materialDesign:Card>

                        <!-- Inventory -->
                        <materialDesign:Card Style="{StaticResource ModernCardStyle}"
                                           Margin="0,0,0,8">
                            <Button Command="{Binding NavigateCommand}"
                                   CommandParameter="Inventory"
                                   Style="{StaticResource NavigationButtonStyle}">
                                <StackPanel Orientation="Horizontal">
                                    <Border Background="#4CAF50"
                                           CornerRadius="8"
                                           Width="32"
                                           Height="32">
                                        <materialDesign:PackIcon Kind="Warehouse"
                                                               Width="18"
                                                               Height="18"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel Margin="16,0,0,0" VerticalAlignment="Center">
                                        <TextBlock Text="Inventory"
                                                  FontWeight="Medium"
                                                  FontSize="14"/>
                                        <TextBlock Text="Stock Management"
                                                  FontSize="11"
                                                  Opacity="0.6"/>
                                    </StackPanel>
                                </StackPanel>
                            </Button>
                        </materialDesign:Card>

                        <!-- Invoices -->
                        <materialDesign:Card Style="{StaticResource ModernCardStyle}"
                                           Margin="0,0,0,8">
                            <Button Command="{Binding NavigateCommand}"
                                   CommandParameter="Invoices"
                                   Style="{StaticResource NavigationButtonStyle}">
                                <StackPanel Orientation="Horizontal">
                                    <Border Background="#E91E63"
                                           CornerRadius="8"
                                           Width="32"
                                           Height="32">
                                        <materialDesign:PackIcon Kind="FileDocument"
                                                               Width="18"
                                                               Height="18"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel Margin="16,0,0,0" VerticalAlignment="Center">
                                        <TextBlock Text="Invoices"
                                                  FontWeight="Medium"
                                                  FontSize="14"/>
                                        <TextBlock Text="Billing and Payments"
                                                  FontSize="11"
                                                  Opacity="0.6"/>
                                    </StackPanel>
                                </StackPanel>
                            </Button>
                        </materialDesign:Card>

                        <!-- Separator -->
                        <Separator Margin="16,16,16,16" Opacity="0.3"/>

                        <TextBlock Text="REPORTS AND ANALYTICS"
                                  Style="{StaticResource SubHeaderTextStyle}"
                                  FontSize="12"
                                  FontWeight="Bold"
                                  Opacity="0.6"
                                  Margin="16,0,0,16"/>

                        <!-- Reports -->
                        <materialDesign:Card Style="{StaticResource ModernCardStyle}"
                                           Margin="0,0,0,8">
                            <Button Command="{Binding NavigateCommand}"
                                   CommandParameter="Reports"
                                   Style="{StaticResource NavigationButtonStyle}">
                                <StackPanel Orientation="Horizontal">
                                    <Border Background="#9C27B0"
                                           CornerRadius="8"
                                           Width="32"
                                           Height="32">
                                        <materialDesign:PackIcon Kind="ChartLine"
                                                               Width="18"
                                                               Height="18"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel Margin="16,0,0,0" VerticalAlignment="Center">
                                        <TextBlock Text="Reports"
                                                  FontWeight="Medium"
                                                  FontSize="14"/>
                                        <TextBlock Text="Business Analytics"
                                                  FontSize="11"
                                                  Opacity="0.6"/>
                                    </StackPanel>
                                </StackPanel>
                            </Button>
                        </materialDesign:Card>

                        <!-- Separator -->
                        <Separator Margin="16,16,16,16" Opacity="0.3"/>

                        <TextBlock Text="SYSTEM"
                                  Style="{StaticResource SubHeaderTextStyle}"
                                  FontSize="12"
                                  FontWeight="Bold"
                                  Opacity="0.6"
                                  Margin="16,0,0,16"/>

                        <!-- Settings -->
                        <materialDesign:Card Style="{StaticResource ModernCardStyle}"
                                           Margin="0,0,0,8">
                            <Button Command="{Binding NavigateCommand}"
                                   CommandParameter="Settings"
                                   Style="{StaticResource NavigationButtonStyle}">
                                <StackPanel Orientation="Horizontal">
                                    <Border Background="#607D8B"
                                           CornerRadius="8"
                                           Width="32"
                                           Height="32">
                                        <materialDesign:PackIcon Kind="Settings"
                                                               Width="18"
                                                               Height="18"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel Margin="16,0,0,0" VerticalAlignment="Center">
                                        <TextBlock Text="Settings"
                                                  FontWeight="Medium"
                                                  FontSize="14"/>
                                        <TextBlock Text="System Configuration"
                                                  FontSize="11"
                                                  Opacity="0.6"/>
                                    </StackPanel>
                                </StackPanel>
                            </Button>
                        </materialDesign:Card>

                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- Content Area -->
            <Grid Grid.Column="1" Background="{DynamicResource MaterialDesignCardBackground}">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="24">
                    <StackPanel>
                        <!-- Page Header -->
                        <Grid Margin="0,0,0,24">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{Binding SelectedModule}"
                                          Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                          FontWeight="Bold"/>
                                <TextBlock Text="Welcome to your business management dashboard"
                                          Style="{StaticResource SubHeaderTextStyle}"
                                          Margin="0,4,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                       Background="{StaticResource PrimaryGradientBrush}"
                                       BorderBrush="{StaticResource PrimaryGradientBrush}"
                                       materialDesign:ButtonAssist.CornerRadius="8"
                                       Margin="8,0">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Plus" Width="16" Height="16"/>
                                        <TextBlock Text="New Invoice" Margin="8,0,0,0"/>
                                    </StackPanel>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                       materialDesign:ButtonAssist.CornerRadius="8">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Download" Width="16" Height="16"/>
                                        <TextBlock Text="Export" Margin="8,0,0,0"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Grid>

                        <!-- KPI Cards -->
                        <Grid Margin="0,0,0,24">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Total Sales -->
                            <materialDesign:Card Grid.Column="0"
                                               Style="{StaticResource ModernCardStyle}"
                                               Background="{StaticResource PrimaryGradientBrush}">
                                <StackPanel>
                                    <Grid>
                                        <materialDesign:PackIcon Kind="TrendingUp"
                                                               Width="24"
                                                               Height="24"
                                                               Foreground="White"
                                                               Opacity="0.8"
                                                               HorizontalAlignment="Right"/>
                                        <StackPanel>
                                            <TextBlock Text="Total Sales"
                                                      Foreground="White"
                                                      FontSize="12"
                                                      Opacity="0.9"/>
                                            <TextBlock Text="$24,580"
                                                      Foreground="White"
                                                      FontSize="24"
                                                      FontWeight="Bold"
                                                      Margin="0,4,0,0"/>
                                            <TextBlock Text="+12.5% from last month"
                                                      Foreground="White"
                                                      FontSize="10"
                                                      Opacity="0.8"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Total Orders -->
                            <materialDesign:Card Grid.Column="1"
                                               Style="{StaticResource ModernCardStyle}"
                                               Background="{StaticResource AccentGradientBrush}">
                                <StackPanel>
                                    <Grid>
                                        <materialDesign:PackIcon Kind="ShoppingCart"
                                                               Width="24"
                                                               Height="24"
                                                               Foreground="White"
                                                               Opacity="0.8"
                                                               HorizontalAlignment="Right"/>
                                        <StackPanel>
                                            <TextBlock Text="Total Orders"
                                                      Foreground="White"
                                                      FontSize="12"
                                                      Opacity="0.9"/>
                                            <TextBlock Text="1,247"
                                                      Foreground="White"
                                                      FontSize="24"
                                                      FontWeight="Bold"
                                                      Margin="0,4,0,0"/>
                                            <TextBlock Text="+8.2% from last month"
                                                      Foreground="White"
                                                      FontSize="10"
                                                      Opacity="0.8"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Low Stock Items -->
                            <materialDesign:Card Grid.Column="2"
                                               Style="{StaticResource ModernCardStyle}"
                                               Background="#FF9800">
                                <StackPanel>
                                    <Grid>
                                        <materialDesign:PackIcon Kind="AlertCircle"
                                                               Width="24"
                                                               Height="24"
                                                               Foreground="White"
                                                               Opacity="0.8"
                                                               HorizontalAlignment="Right"/>
                                        <StackPanel>
                                            <TextBlock Text="Low Stock Items"
                                                      Foreground="White"
                                                      FontSize="12"
                                                      Opacity="0.9"/>
                                            <TextBlock Text="23"
                                                      Foreground="White"
                                                      FontSize="24"
                                                      FontWeight="Bold"
                                                      Margin="0,4,0,0"/>
                                            <TextBlock Text="Requires attention"
                                                      Foreground="White"
                                                      FontSize="10"
                                                      Opacity="0.8"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Active Customers -->
                            <materialDesign:Card Grid.Column="3"
                                               Style="{StaticResource ModernCardStyle}"
                                               Background="#4CAF50">
                                <StackPanel>
                                    <Grid>
                                        <materialDesign:PackIcon Kind="AccountMultiple"
                                                               Width="24"
                                                               Height="24"
                                                               Foreground="White"
                                                               Opacity="0.8"
                                                               HorizontalAlignment="Right"/>
                                        <StackPanel>
                                            <TextBlock Text="Active Customers"
                                                      Foreground="White"
                                                      FontSize="12"
                                                      Opacity="0.9"/>
                                            <TextBlock Text="892"
                                                      Foreground="White"
                                                      FontSize="24"
                                                      FontWeight="Bold"
                                                      Margin="0,4,0,0"/>
                                            <TextBlock Text="+15 new this month"
                                                      Foreground="White"
                                                      FontSize="10"
                                                      Opacity="0.8"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>
                        </Grid>

                        <!-- Quick Actions and Recent Activity -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Recent Transactions -->
                            <materialDesign:Card Grid.Column="0"
                                               Style="{StaticResource ModernCardStyle}"
                                               Margin="0,0,12,0">
                                <StackPanel>
                                    <TextBlock Text="Recent Transactions"
                                              Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                              FontWeight="Bold"
                                              Margin="0,0,0,16"/>

                                    <!-- Transaction Items -->
                                    <ItemsControl>
                                        <ListBoxItem>
                                            <Grid Margin="0,8">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <Border Grid.Column="0"
                                                       Background="{StaticResource PrimaryGradientBrush}"
                                                       CornerRadius="16"
                                                       Width="32"
                                                       Height="32">
                                                    <materialDesign:PackIcon Kind="Receipt"
                                                                           Width="16"
                                                                           Height="16"
                                                                           Foreground="White"
                                                                           HorizontalAlignment="Center"
                                                                           VerticalAlignment="Center"/>
                                                </Border>

                                                <StackPanel Grid.Column="1" Margin="12,0">
                                                    <TextBlock Text="Invoice #INV-2024-001" FontWeight="Medium"/>
                                                    <TextBlock Text="John Doe - 2 hours ago"
                                                              FontSize="12"
                                                              Opacity="0.6"/>
                                                </StackPanel>

                                                <TextBlock Grid.Column="2"
                                                          Text="$1,250.00"
                                                          FontWeight="Bold"
                                                          Foreground="{StaticResource PrimaryGradientBrush}"/>
                                            </Grid>
                                        </ListBoxItem>

                                        <ListBoxItem>
                                            <Grid Margin="0,8">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <Border Grid.Column="0"
                                                       Background="{StaticResource AccentGradientBrush}"
                                                       CornerRadius="16"
                                                       Width="32"
                                                       Height="32">
                                                    <materialDesign:PackIcon Kind="CashMultiple"
                                                                           Width="16"
                                                                           Height="16"
                                                                           Foreground="White"
                                                                           HorizontalAlignment="Center"
                                                                           VerticalAlignment="Center"/>
                                                </Border>

                                                <StackPanel Grid.Column="1" Margin="12,0">
                                                    <TextBlock Text="Payment Received" FontWeight="Medium"/>
                                                    <TextBlock Text="ABC Corp - 4 hours ago"
                                                              FontSize="12"
                                                              Opacity="0.6"/>
                                                </StackPanel>

                                                <TextBlock Grid.Column="2"
                                                          Text="$850.00"
                                                          FontWeight="Bold"
                                                          Foreground="#4CAF50"/>
                                            </Grid>
                                        </ListBoxItem>
                                    </ItemsControl>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Quick Actions -->
                            <materialDesign:Card Grid.Column="1"
                                               Style="{StaticResource ModernCardStyle}"
                                               Margin="12,0,0,0">
                                <StackPanel>
                                    <TextBlock Text="Quick Actions"
                                              Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                              FontWeight="Bold"
                                              Margin="0,0,0,16"/>

                                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                           HorizontalAlignment="Stretch"
                                           Margin="0,0,0,8"
                                           materialDesign:ButtonAssist.CornerRadius="8">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16"/>
                                            <TextBlock Text="Add Product" Margin="8,0,0,0"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                           HorizontalAlignment="Stretch"
                                           Margin="0,0,0,8"
                                           materialDesign:ButtonAssist.CornerRadius="8">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="AccountPlus" Width="16" Height="16"/>
                                            <TextBlock Text="Add Customer" Margin="8,0,0,0"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                           HorizontalAlignment="Stretch"
                                           Margin="0,0,0,8"
                                           materialDesign:ButtonAssist.CornerRadius="8">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16"/>
                                            <TextBlock Text="Create Invoice" Margin="8,0,0,0"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                           HorizontalAlignment="Stretch"
                                           materialDesign:ButtonAssist.CornerRadius="8">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ChartLine" Width="16" Height="16"/>
                                            <TextBlock Text="View Reports" Margin="8,0,0,0"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </materialDesign:Card>
                        </Grid>
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2"
                Background="{DynamicResource MaterialDesignCardBackground}"
                BorderBrush="{DynamicResource MaterialDesignDivider}"
                BorderThickness="0,1,0,0"
                Padding="24,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Status Indicator -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Ellipse Width="8" Height="8" Fill="#4CAF50" VerticalAlignment="Center"/>
                    <TextBlock Text="System Online"
                              FontSize="12"
                              VerticalAlignment="Center"
                              Margin="8,0,0,0"/>
                </StackPanel>

                <!-- Connection Status -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="16,0">
                    <materialDesign:PackIcon Kind="Database"
                                           Width="14"
                                           Height="14"
                                           VerticalAlignment="Center"
                                           Foreground="#4CAF50"/>
                    <TextBlock Text="Database Connected"
                              FontSize="12"
                              VerticalAlignment="Center"
                              Margin="4,0,0,0"/>
                </StackPanel>

                <!-- Current Time -->
                <StackPanel Grid.Column="3" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Clock"
                                           Width="14"
                                           Height="14"
                                           VerticalAlignment="Center"
                                           Opacity="0.6"/>
                    <TextBlock Text="{Binding Source={x:Static System:DateTime.Now}, StringFormat='MMM dd, yyyy - HH:mm'}"
                              FontSize="12"
                              VerticalAlignment="Center"
                              Margin="4,0,0,0"
                              Opacity="0.8"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
